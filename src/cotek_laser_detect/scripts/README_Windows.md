# 激光雷达标定工具 - Windows使用指南

## 系统要求
- Windows 7/8/10/11
- Python 3.7 或更高版本

## 安装步骤

### 1. 安装Python
如果您的系统还没有安装Python：
1. 访问 https://www.python.org/downloads/
2. 下载最新版本的Python
3. 运行安装程序，**确保勾选"Add Python to PATH"**

### 2. 安装依赖包
双击运行 `install_dependencies_windows.bat` 文件，它会自动安装所需的依赖包：
- PyQt5 (图形界面库)
- PyYAML (YAML文件处理库)

### 3. 运行程序
双击运行 `run_lidar_calib_tool.bat` 文件启动激光雷达标定工具。

## 手动运行
如果批处理文件无法正常工作，您也可以手动运行：

1. 打开命令提示符（cmd）或PowerShell
2. 导航到脚本所在目录
3. 运行命令：
   ```
   python lidar_calib_tool.py
   ```

## 功能说明
- **激光雷达编号输入**：输入要标定的激光雷达编号
- **偏移方向选择**：选择1.428距离的偏移方向（上/下/左/右）
- **快速坐标输入**：支持批量粘贴激光雷达坐标和世界坐标
- **手动坐标输入**：支持手动输入3个对应点的坐标
- **YAML生成**：自动生成标定用的YAML配置文件

## 输出文件
生成的YAML文件将保存在 `yaml_calib` 目录下，文件名格式为 `lidar{编号}.yaml`

## 故障排除

### 中文显示问题
程序已针对Windows系统优化了中文字体显示，使用Microsoft YaHei字体。

### 依赖包安装失败
如果自动安装失败，请手动运行：
```
pip install PyQt5 PyYAML
```

### Python未找到错误
确保Python已正确安装并添加到系统PATH中。可以在命令提示符中运行 `python --version` 验证。

## 联系支持
如有问题，请联系技术支持团队。
