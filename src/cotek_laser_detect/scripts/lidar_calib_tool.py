import os
import sys
import yaml
import re
from PyQt5.QtWidgets import (QApplication, QWidget, QLabel, QLineEdit, 
                            QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton, 
                            QRadioButton, QButtonGroup, QGroupBox, QMessageBox,
                            QTextEdit)
from PyQt5.QtCore import Qt

# Custom YAML representer for lists to create the desired format
def represent_list(self, data):
    return yaml.nodes.ScalarNode('tag:yaml.org,2002:str', str(data).replace("'", ""))

class LidarCalibTool(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        
    def initUI(self):
        # Main layout
        main_layout = QVBoxLayout()
        
        # Lidar number input
        lidar_layout = QHBoxLayout()
        lidar_layout.addWidget(QLabel("激光雷达编号:"))
        self.lidar_number = QLineEdit()
        lidar_layout.addWidget(self.lidar_number)
        main_layout.addLayout(lidar_layout)

        # Offset direction selection
        offset_group = QGroupBox("偏移方向 (1.428 距离)")
        offset_layout = QHBoxLayout()

        self.direction_group = QButtonGroup(self)
        self.up_radio = QRadioButton("上")
        self.down_radio = QRadioButton("下")
        self.left_radio = QRadioButton("左")
        self.right_radio = QRadioButton("右")
        
        self.direction_group.addButton(self.up_radio, 1)
        self.direction_group.addButton(self.down_radio, 2)
        self.direction_group.addButton(self.left_radio, 3)
        self.direction_group.addButton(self.right_radio, 4)
        
        self.up_radio.setChecked(True)
        
        offset_layout.addWidget(self.up_radio)
        offset_layout.addWidget(self.down_radio)
        offset_layout.addWidget(self.left_radio)
        offset_layout.addWidget(self.right_radio)
        
        offset_group.setLayout(offset_layout)
        main_layout.addWidget(offset_group)

        # Quick Lidar Point Entry section
        quick_lidar_group = QGroupBox("快速激光雷达点输入")
        quick_lidar_layout = QVBoxLayout()

        # Replace LineEdit with TextEdit for multiple lines
        lidar_paste_layout = QVBoxLayout()
        lidar_paste_layout.addWidget(QLabel("粘贴激光雷达坐标:"))
        self.pasted_lidar_coords = QTextEdit()
        self.pasted_lidar_coords.setPlaceholderText("[20:27:12] [Picked] - P#46090 (1.822000;-1.667000;0.817000)\n[20:28:07] [Picked] - P#20499 (-3.324000;-0.388000;0.795000)")
        self.pasted_lidar_coords.setMaximumHeight(100)
        lidar_paste_layout.addWidget(self.pasted_lidar_coords)
        quick_lidar_layout.addLayout(lidar_paste_layout)

        # Lidar input buttons
        lidar_buttons_layout = QHBoxLayout()
        self.apply_lidar_btn = QPushButton("应用激光雷达点")
        self.apply_lidar_btn.clicked.connect(self.apply_lidar_points)
        lidar_buttons_layout.addWidget(self.apply_lidar_btn)

        self.clear_lidar_input_btn = QPushButton("清除输入")
        self.clear_lidar_input_btn.clicked.connect(self.clear_lidar_input)
        lidar_buttons_layout.addWidget(self.clear_lidar_input_btn)
        
        quick_lidar_layout.addLayout(lidar_buttons_layout)
        quick_lidar_group.setLayout(quick_lidar_layout)
        main_layout.addWidget(quick_lidar_group)
        
        # Quick World Point Entry section
        quick_world_group = QGroupBox("快速世界坐标点输入")
        quick_world_layout = QVBoxLayout()

        world_paste_layout = QVBoxLayout()
        world_paste_layout.addWidget(QLabel("粘贴世界坐标:"))
        self.pasted_world_coords = QTextEdit()
        self.pasted_world_coords.setPlaceholderText("当前位置: (69.79, -76.161, -179.536)\n当前位置: (74.626, -78.397, -179.594)")
        self.pasted_world_coords.setMaximumHeight(100)
        world_paste_layout.addWidget(self.pasted_world_coords)
        quick_world_layout.addLayout(world_paste_layout)

        # World input buttons
        world_buttons_layout = QHBoxLayout()
        self.apply_world_btn = QPushButton("应用世界坐标点")
        self.apply_world_btn.clicked.connect(self.apply_world_points)
        world_buttons_layout.addWidget(self.apply_world_btn)

        self.clear_world_input_btn = QPushButton("清除输入")
        self.clear_world_input_btn.clicked.connect(self.clear_world_input)
        world_buttons_layout.addWidget(self.clear_world_input_btn)
        
        quick_world_layout.addLayout(world_buttons_layout)
        quick_world_group.setLayout(quick_world_layout)
        main_layout.addWidget(quick_world_group)
        
        # Points input section
        points_layout = QGridLayout()
        
        # Headers
        points_layout.addWidget(QLabel("点号"), 0, 0)
        points_layout.addWidget(QLabel("激光雷达点 (x, y, z)"), 0, 1, 1, 3)
        points_layout.addWidget(QLabel("世界坐标点 (x, y, z)"), 0, 4, 1, 3)
        
        # Create input fields for 3 points
        self.lidar_points = []
        self.world_points = []
        
        for i in range(3):
            # Point number
            points_layout.addWidget(QLabel(f"{i+1}"), i+1, 0)
            
            # Lidar points
            lidar_x = QLineEdit()
            lidar_y = QLineEdit()
            lidar_z = QLineEdit()
            
            points_layout.addWidget(lidar_x, i+1, 1)
            points_layout.addWidget(lidar_y, i+1, 2)
            points_layout.addWidget(lidar_z, i+1, 3)
            
            self.lidar_points.append((lidar_x, lidar_y, lidar_z))
            
            # World points
            world_x = QLineEdit("") # Default value
            world_y = QLineEdit("") # Default value
            world_z = QLineEdit("2.4") # Default value, now editable

            points_layout.addWidget(world_x, i+1, 4)
            points_layout.addWidget(world_y, i+1, 5)
            points_layout.addWidget(world_z, i+1, 6) # Now editable Z input

            self.world_points.append((world_x, world_y, world_z)) # Now stores x, y, z
        
        main_layout.addLayout(points_layout)
        
        # Clear buttons
        clear_buttons_layout = QHBoxLayout()
        self.clear_lidar_btn = QPushButton("清除激光雷达点")
        self.clear_lidar_btn.clicked.connect(self.clear_lidar_points)
        clear_buttons_layout.addWidget(self.clear_lidar_btn)

        self.clear_world_btn = QPushButton("清除世界坐标点")
        self.clear_world_btn.clicked.connect(self.clear_world_points)
        clear_buttons_layout.addWidget(self.clear_world_btn)
        main_layout.addLayout(clear_buttons_layout)

        # Generate button
        self.generate_btn = QPushButton("生成 YAML")
        self.generate_btn.clicked.connect(self.generate_yaml)
        main_layout.addWidget(self.generate_btn)

        self.setLayout(main_layout)
        self.setWindowTitle('激光雷达标定工具')
        self.resize(1200, 600)  # Increased width for better display
        self.show()
    
    def clear_world_points(self):
        for wx_edit, wy_edit, wz_edit in self.world_points:
            wx_edit.clear()
            wy_edit.clear()
            wz_edit.setText("2.4")  # Reset to default value

    def clear_lidar_points(self):
        for lx_edit, ly_edit, lz_edit in self.lidar_points:
            lx_edit.clear()
            ly_edit.clear()
            lz_edit.clear()
            
    def clear_lidar_input(self):
        self.pasted_lidar_coords.clear()
        
    def clear_world_input(self):
        self.pasted_world_coords.clear()

    def get_offset_direction(self):
        if self.up_radio.isChecked():
            return "up"
        elif self.down_radio.isChecked():
            return "down"
        elif self.left_radio.isChecked():
            return "left"
        elif self.right_radio.isChecked():
            return "right"
        return "up"  # Default
    
    def apply_lidar_points(self):
        text = self.pasted_lidar_coords.toPlainText().strip()
        if not text:
            return
            
        lines = text.split('\n')
        
        # Process up to 3 points
        points_to_process = min(3, len(lines))
        
        for i in range(points_to_process):
            line = lines[i]
            # Extract coordinates using regex - look for numbers inside parentheses
            match = re.search(r'\(([^)]+)\)', line)
            if not match:
                continue
                
            coords_str = match.group(1)
            # Split by either semicolon or comma
            coords = re.split(r'[;,]', coords_str)
            
            if len(coords) < 3:
                continue
                
            try:
                x_val = float(coords[0].strip())
                y_val = float(coords[1].strip())
                z_val = float(coords[2].strip())
                
                # Update the corresponding fields
                self.lidar_points[i][0].setText(str(x_val))
                self.lidar_points[i][1].setText(str(y_val))
                self.lidar_points[i][2].setText(str(z_val))
                
            except (ValueError, IndexError):
                continue
    
    def apply_world_points(self):
        text = self.pasted_world_coords.toPlainText().strip()
        if not text:
            return
            
        lines = text.split('\n')
        
        # Process up to 3 points
        points_to_process = min(3, len(lines))
        
        for i in range(points_to_process):
            line = lines[i]
            # Extract coordinates using regex - look for numbers inside parentheses
            match = re.search(r'\(([^)]+)\)', line)
            if not match:
                continue
                
            coords_str = match.group(1)
            # Split by either semicolon or comma
            coords = re.split(r'[;,]', coords_str)
            
            if len(coords) < 2:
                continue
                
            try:
                x_val = float(coords[0].strip())
                y_val = float(coords[1].strip())

                # Update the corresponding fields - x and y
                self.world_points[i][0].setText(str(x_val))
                self.world_points[i][1].setText(str(y_val))

                # If z coordinate is provided, update it too
                if len(coords) >= 3:
                    z_val = float(coords[2].strip())
                    self.world_points[i][2].setText(str(z_val))

            except (ValueError, IndexError):
                continue

    def apply_pasted_lidar_point(self):
        # Legacy method - keep for compatibility
        pasted_text = self.pasted_lidar_coord_input.text().strip()
        target_index = self.target_lidar_point_group.checkedId()

        if target_index == -1: # Should not happen if one is checked by default
            QMessageBox.warning(self, "选择错误", "请选择目标激光雷达点 (1, 2, 或 3)。")
            return

        if not (pasted_text.startswith('(') and pasted_text.endswith(')')):
            QMessageBox.warning(self, "输入错误", "粘贴的坐标必须是 (x;y;z) 格式。缺少括号。")
            return

        content_between_parentheses = pasted_text[1:-1]
        parts = content_between_parentheses.split(';')

        if len(parts) != 3:
            QMessageBox.warning(self, "输入错误", "粘贴的坐标必须有三个用分号分隔的值 (例如: (x.xxx;y.yyy;z.zzz))。")
            return
        
        try:
            lx_val = float(parts[0].strip())
            ly_val = float(parts[1].strip())
            lz_val = float(parts[2].strip())

            lidar_x_edit, lidar_y_edit, lidar_z_edit = self.lidar_points[target_index]
            lidar_x_edit.setText(str(lx_val))
            lidar_y_edit.setText(str(ly_val))
            lidar_z_edit.setText(str(lz_val))
            
        except ValueError:
            QMessageBox.warning(self, "输入错误", "粘贴坐标中的数字格式无效。请为 x, y, z 使用有效数字。")
            return

    def apply_offset(self, x, y, direction):
        offset = 1.428
        if direction == "up":
            return x, y + offset
        elif direction == "down":
            return x, y - offset
        elif direction == "left":
            return x - offset, y
        elif direction == "right":
            return x + offset, y
    
    def generate_yaml(self):
        try:
            # Get lidar number
            lidar_num = self.lidar_number.text().strip()
            if not lidar_num:
                QMessageBox.warning(self, "输入错误", "请输入激光雷达编号")
                return
            
            # Get direction
            direction = self.get_offset_direction()
            
            # Process data for yaml output
            points_data = []
            
            # Process each point
            for i in range(3):
                try:
                    # Get lidar point
                    lx = float(self.lidar_points[i][0].text())
                    ly = float(self.lidar_points[i][1].text())
                    lz = float(self.lidar_points[i][2].text())
                    
                    # Get world point
                    wx = float(self.world_points[i][0].text())
                    wy = float(self.world_points[i][1].text())
                    wz = float(self.world_points[i][2].text())

                    # Apply offset
                    wx_offset, wy_offset = self.apply_offset(wx, wy, direction)

                    # Add to data
                    point_data = {
                        "lidar_point": [lx, ly, lz],
                        "world_point": [wx_offset, wy_offset, wz]
                    }
                    points_data.append(point_data)
                    
                except ValueError:
                    QMessageBox.warning(self, "输入错误", f"第 {i+1} 个点的输入无效")
                    return
            
            # Create directory if it doesn't exist
            os.makedirs("yaml_calib", exist_ok=True)
            
            filename = os.path.join("yaml_calib", f"lidar{lidar_num}.yaml")

            # Check if file exists and prompt for overwrite
            if os.path.exists(filename):
                reply = QMessageBox.question(self, '文件已存在',
                                             f"文件 {filename} 已存在。您要覆盖它吗？",
                                             QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.No:
                    QMessageBox.information(self, "已取消", "文件生成已取消。")
                    return

            # Create the yaml structure
            data = {
                f"lidar{lidar_num}": {
                    "points": points_data
                }
            }
            
            # Write YAML file with custom formatting
            with open(filename, 'w') as f:
                # First write the header
                f.write(f"lidar{lidar_num}:\n")
                f.write("  points:\n")
                
                # Then write each point with the desired formatting
                for point in points_data:
                    f.write(f"    - lidar_point: {point['lidar_point']}\n")
                    f.write(f"      world_point: {point['world_point']}\n")
                    f.write("\n")
            
            QMessageBox.information(self, "成功", f"YAML 文件已保存到 {filename}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"发生错误: {str(e)}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = LidarCalibTool()
    sys.exit(app.exec_()) 
